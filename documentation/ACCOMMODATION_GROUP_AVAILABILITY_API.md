# Accommodation Group Availability API

## Overview

The Accommodation Group Availability API allows you to check availability across all accommodations within a specific accommodation group for given dates. This is useful for scenarios where you want to show all available options within a property collection or group.

## Endpoint

```
POST /api/accommodation-groups/{groupId}/check-availability
```

## Authentication

This endpoint requires authentication using Laravel Sanctum with the `widget-access` ability.

```
Authorization: Bearer {your-widget-token}
```

## Request Parameters

### Path Parameters
- `groupId` (integer, required): The ID of the accommodation group

### Request Body (JSON)
- `start_date` (string, required): Check-in date in Y-m-d format (e.g., "2024-07-15")
- `end_date` (string, required): Check-out date in Y-m-d format (e.g., "2024-07-20")
- `number_of_persons` (integer, optional): Number of persons (defaults to minimum occupancy of each accommodation)

## Response Format

### Success Response (200)

```json
{
  "group_id": 1,
  "group_name": "Beach House Collection",
  "has_availability": true,
  "total_accommodations": 5,
  "available_accommodations": 3,
  "accommodations": [
    {
      "id": 1,
      "name": "Ocean View Villa",
      "available": true,
      "total_price": "1500.00",
      "reason": "available",
      "message": "Accommodation is available for the specified dates",
      "details": {
        "daily_prices": {
          "2024-07-15": 300.00,
          "2024-07-16": 300.00,
          "2024-07-17": 300.00,
          "2024-07-18": 300.00,
          "2024-07-19": 300.00
        },
        "total_price": 1500.00,
        "currency": "R",
        "occupancy": 2,
        "min_occupancy": 1,
        "max_occupancy": 4
      }
    },
    {
      "id": 2,
      "name": "Garden Cottage",
      "available": false,
      "total_price": null,
      "reason": "existing_booking",
      "message": "Accommodation is not available for the specified dates",
      "details": null
    }
  ]
}
```

### Response Fields

- `group_id`: ID of the accommodation group
- `group_name`: Name of the accommodation group
- `has_availability`: Boolean indicating if at least one accommodation is available
- `total_accommodations`: Total number of accommodations in the group
- `available_accommodations`: Number of accommodations that are available
- `accommodations`: Array of accommodation availability results

#### Accommodation Object Fields

- `id`: Accommodation ID
- `name`: Accommodation name
- `available`: Boolean indicating availability
- `total_price`: Total price for the stay (null if not available)
- `reason`: Reason code for availability status
- `message`: Human-readable message about availability
- `details`: Additional details (pricing breakdown for available accommodations)

### Reason Codes

- `available`: Accommodation is available
- `minimum_notice`: Booking doesn't meet minimum booking notice requirement
- `minimum_stay`: Stay duration is below minimum stay requirement
- `existing_booking`: Accommodation has conflicting bookings
- `blocked_period`: Accommodation has unavailable periods blocking the dates
- `occupancy_limit`: Requested occupancy is outside allowed limits

## Error Responses

### Validation Error (422)
```json
{
  "message": "The given data was invalid.",
  "errors": {
    "start_date": ["The start date field is required."],
    "end_date": ["The end date must be a date after start date."]
  }
}
```

### Not Found (404)
```json
{
  "message": "Accommodation group not found or you do not have permission to access it"
}
```

### Unauthorized (401)
```json
{
  "message": "Unauthenticated."
}
```

## Usage Examples

### Basic Availability Check

```javascript
const response = await fetch('/api/accommodation-groups/1/check-availability', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer your-widget-token',
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    start_date: '2024-07-15',
    end_date: '2024-07-20',
    number_of_persons: 2
  })
});

const data = await response.json();

if (data.has_availability) {
  console.log(`${data.available_accommodations} out of ${data.total_accommodations} accommodations available`);
  
  // Show available accommodations
  data.accommodations
    .filter(acc => acc.available)
    .forEach(acc => {
      console.log(`${acc.name}: R${acc.total_price}`);
    });
}
```

### Filtering Available Accommodations

```javascript
const availableAccommodations = data.accommodations.filter(acc => acc.available);
const unavailableAccommodations = data.accommodations.filter(acc => !acc.available);

console.log('Available:', availableAccommodations.length);
console.log('Unavailable:', unavailableAccommodations.length);
```

## Integration Notes

1. **Widget Integration**: This endpoint is designed for widget use and requires the `widget-access` token ability.

2. **Pricing**: Prices are calculated based on the accommodation's pricing rules, including date-specific and occupancy-based pricing.

3. **Availability Logic**: The same availability logic used for individual accommodations is applied to each accommodation in the group, including:
   - Minimum booking notice periods
   - Minimum stay requirements
   - Existing bookings
   - Unavailable periods
   - Occupancy limits

4. **Performance**: The endpoint checks all accommodations in the group sequentially. For large groups, consider implementing caching or pagination if needed.

5. **Permissions**: Only published accommodation groups and their published accommodations are returned for widget requests.
