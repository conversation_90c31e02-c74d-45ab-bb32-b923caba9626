<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('accommodation_searches', function (Blueprint $table) {
            $table->id();
            $table->foreignId('accommodation_id')->constrained()->cascadeOnDelete();
            $table->foreignId('user_id')->constrained()->cascadeOnDelete(); // Owner of accommodation
            $table->date('start_date');
            $table->date('end_date');
            $table->integer('requested_occupancy')->nullable();
            $table->boolean('was_available');
            $table->string('unavailability_reason')->nullable();
            $table->decimal('quoted_price', 10, 2)->nullable();
            $table->timestamp('searched_at');
            
            // Optimization indexes
            $table->index(['accommodation_id', 'searched_at']);
            $table->index(['user_id', 'searched_at']);
            $table->index(['start_date', 'end_date']);
        });
    }
};