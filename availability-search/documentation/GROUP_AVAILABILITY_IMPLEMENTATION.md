# Group Availability Search Implementation

## Overview

This document describes the implementation of group-wide availability search functionality for the AvailabilitySearch widget component. The implementation allows users to search for available accommodations across an entire accommodation group rather than browsing individual accommodations.

## Architecture Changes

### New Components

1. **GroupAvailabilitySearch.vue**
   - Main component for group availability search functionality
   - Includes date picker, occupancy selector, and search button
   - Handles API calls to the group availability endpoint
   - Manages booking flow for selected accommodations
   - Location: `src/components/availability-search/GroupAvailabilitySearch.vue`

2. **AvailableAccommodationsList.vue**
   - Displays search results using AccommodationCard components
   - Shows available accommodations with "Book Now" buttons
   - Displays unavailable accommodations with reasons
   - Location: `src/components/availability-search/AvailableAccommodationsList.vue`

### Modified Components

1. **AvailabilitySearchMain.vue**
   - Added conditional rendering for group search vs. traditional accommodation list
   - Imports and uses GroupAvailabilitySearch when `groupId` is provided
   - Added event handling for search completion

2. **AccommodationCard.vue**
   - Added `buttonMode` prop to support "Book Now" vs "Check Availability" modes
   - Added `totalPrice` and `currency` props for displaying booking prices
   - Enhanced button click handling to emit different events based on mode

3. **AvailabilitySearch.ce.vue**
   - Added CSS import for group availability search styles

## API Integration

### Endpoint Used
```
POST /api/accommodation-groups/{groupId}/check-availability
```

### Request Format
```json
{
  "start_date": "2024-07-15",
  "end_date": "2024-07-20", 
  "number_of_persons": 2
}
```

### Response Handling
- Processes accommodation availability results
- Filters available vs unavailable accommodations
- Transforms accommodation data for display components
- Handles error responses with user-friendly messages

## User Flow

### Group Search Mode (when groupId is provided)
1. User sees date picker and occupancy selector
2. User selects dates and number of guests
3. User clicks "Search Availability"
4. System calls group availability API
5. Results show available accommodations with "Book Now" buttons
6. User clicks "Book Now" on desired accommodation
7. Booking form opens with pricing details
8. User completes booking form and submits
9. Booking confirmation is displayed

### Traditional Mode (when groupId is not provided)
- Maintains existing functionality unchanged
- Shows accommodation list with "Check Availability" buttons
- Individual accommodation availability checking

## Error Handling

### API Errors
- 404: Group not found or no permission
- 422: Validation errors (invalid dates, etc.)
- 401: Authentication failures
- 500: Server errors

### User-Friendly Messages
- No accommodations available for selected dates
- Specific unavailability reasons (minimum stay, existing bookings, etc.)
- Network connectivity issues
- Retry functionality for failed searches

## Styling

### New CSS File
- `src/components/availability-search/styles/group-availability-search.css`
- Responsive design for mobile and desktop
- Consistent with existing component styling
- Error state and loading state styling

### CSS Variables Used
- `--color-primary`: Main brand color
- `--color-accent-cta`: Call-to-action button color
- `--color-accent-primary`: Secondary accent color
- `--spacing-*`: Consistent spacing variables
- `--border-radius`: Consistent border radius

## Testing

### Test File
- `test-group-search.html`: Manual testing page
- Demonstrates widget integration with group search
- Event logging for debugging
- Customizable styling via CSS variables

### Test Scenarios
1. **Successful Search**: Valid dates with available accommodations
2. **No Availability**: Valid dates with no available accommodations
3. **API Errors**: Invalid group ID, authentication failures
4. **Booking Flow**: Complete booking process from search to confirmation
5. **Responsive Design**: Mobile and desktop layouts

## Configuration

### Props
- `siteId`: Required for authentication
- `groupId`: When provided, enables group search mode
- CSS variables for theming

### Environment Variables
- `VITE_API_BASE_URL`: Backend API base URL

## Backward Compatibility

- Existing functionality remains unchanged when `groupId` is not provided
- All existing props and events continue to work
- No breaking changes to the public API

## Future Enhancements

### Potential Improvements
1. **Enhanced Accommodation Details**: Fetch full accommodation details (images, descriptions) for search results
2. **Filtering Options**: Add filters for price range, amenities, etc.
3. **Sorting Options**: Sort results by price, rating, availability
4. **Calendar Integration**: Show availability calendar for the entire group
5. **Bulk Booking**: Allow booking multiple accommodations in one transaction

### API Enhancements
1. **Accommodation Details**: Include full accommodation data in group availability response
2. **Group Calendar Data**: Endpoint for group-level calendar restrictions
3. **Advanced Filtering**: Server-side filtering and sorting options

## Events Emitted

### New Events
- `bookingbear:search-completed`: Fired when group availability search completes
- Enhanced error events with specific error types

### Existing Events
- All existing events continue to work unchanged
- `bookingbear:booking-created`: Works for both individual and group search bookings

## Dependencies

### External Libraries
- Vue 3 (existing)
- @sebarkar/vue3-hotel-datepicker (existing)

### Internal Dependencies
- All existing components (DateRangePicker, OccupancySelector, BookingForm, etc.)
- Existing authentication and API handling logic
