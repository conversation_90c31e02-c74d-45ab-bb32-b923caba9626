.group-availability-search {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-lg);
}

.search-form {
  background: white;
  padding: var(--spacing-lg);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  margin-bottom: var(--spacing-lg);
}

.search-form h3 {
  margin: 0 0 var(--spacing-lg) 0;
  color: var(--color-primary);
  font-size: 1.5em;
}

.search-availability-button {
  background: var(--color-accent-cta);
  color: white;
  border: none;
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--border-radius);
  font-size: 1em;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: var(--spacing-md);
  width: 100%;
  max-width: 300px;
}

.search-availability-button:hover:not(:disabled) {
  background: #e56b7a;
  transform: translateY(-1px);
}

.search-availability-button:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}

.search-results {
  margin-top: var(--spacing-lg);
}

.results-summary h4 {
  color: var(--color-primary);
  margin: 0 0 var(--spacing-sm) 0;
  font-size: 1.3em;
}

.results-summary p {
  color: var(--color-text-light);
  margin: 0 0 var(--spacing-lg) 0;
}

.no-availability {
  background: white;
  padding: var(--spacing-lg);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  text-align: center;
}

.availability-icon {
  font-size: 3em;
  color: #e74c3c;
  margin-bottom: var(--spacing-md);
}

.availability-message h4 {
  color: var(--color-primary);
  margin: 0 0 var(--spacing-md) 0;
}

.availability-message p {
  color: var(--color-text-light);
  margin: 0 0 var(--spacing-sm) 0;
}

.unavailability-reasons {
  margin-top: var(--spacing-lg);
  text-align: left;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

.unavailability-reasons h5 {
  color: var(--color-primary);
  margin: 0 0 var(--spacing-sm) 0;
}

.unavailability-reasons ul {
  margin: 0;
  padding-left: var(--spacing-lg);
}

.unavailability-reasons li {
  color: var(--color-text-light);
  margin-bottom: var(--spacing-sm);
}

.search-error {
  background: white;
  padding: var(--spacing-lg);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  text-align: center;
  border-left: 4px solid #e74c3c;
}

.error-icon {
  font-size: 3em;
  color: #e74c3c;
  margin-bottom: var(--spacing-md);
}

.error-message h4 {
  color: #e74c3c;
  margin: 0 0 var(--spacing-md) 0;
}

.error-message p {
  color: var(--color-text-light);
  margin: 0 0 var(--spacing-md) 0;
}

.retry-button {
  background: var(--color-accent-primary);
  color: white;
  border: none;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: background 0.2s ease;
}

.retry-button:hover {
  background: #007a5e;
}

.booking-section {
  background: white;
  padding: var(--spacing-lg);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  margin-top: var(--spacing-lg);
}

.booking-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--color-border);
}

.back-button {
  background: none;
  border: 1px solid var(--color-border);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius);
  cursor: pointer;
  color: var(--color-text);
  transition: all 0.2s ease;
}

.back-button:hover {
  background: var(--color-background);
  border-color: var(--color-primary);
}

.booking-header h4 {
  margin: 0;
  color: var(--color-primary);
  font-size: 1.3em;
}

.booking-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--spacing-lg);
}

@media (max-width: 768px) {
  .group-availability-search {
    padding: var(--spacing-md);
  }
  
  .search-form {
    padding: var(--spacing-md);
  }
  
  .search-availability-button {
    max-width: none;
  }
  
  .booking-content {
    grid-template-columns: 1fr;
  }
  
  .booking-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }
}
