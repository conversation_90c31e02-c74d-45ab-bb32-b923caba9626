<template>
  <div class="booking-bear-component">
    <LoadingState v-if="loading" />

    <ErrorState
      v-else-if="error"
      :error="error"
      @retry="fetchData"
    />

    <div v-else class="content" data-state="loaded">
      <slot name="header" :accommodations="accommodations"></slot>

      <!-- Group Availability Search Mode -->
      <div v-if="props.groupId && useGroupSearch" class="group-search-container">
        <GroupAvailabilitySearch
          :groupId="props.groupId"
          :apiBaseUrl="API_BASE_URL"
          :authToken="authToken"
          :componentVersion="componentVersion"
          @search-completed="handleSearchCompleted"
          @booking-created="handleBookingCreated"
          @error="handleError"
          @show-terms="showTerms = true"
        />
      </div>

      <!-- Traditional Accommodation List Mode -->
      <div v-else class="accommodations-container">
        <div v-if="!selectedAccommodation">
          <AccommodationList
            :accommodations="accommodations"
            :apiBaseUrl="API_BASE_URL"
            @select="selectAccommodation"
          />
        </div>

        <div v-else class="selected-accommodation">
          <button class="back-button" @click="selectedAccommodation = null">← Back to list</button>

          <AccommodationDetail
            :accommodation="selectedAccommodation"
            :apiBaseUrl="API_BASE_URL"
          />

          <AvailabilityChecker
            :accommodation="selectedAccommodation"
            :apiBaseUrl="API_BASE_URL"
            :authToken="authToken"
            :componentVersion="componentVersion"
            @availability-result="handleAvailabilityResult"
            @booking-created="handleBookingCreated"
            @error="handleError"
            @show-terms="showTerms = true"
            @reset="resetBooking"
          />
        </div>
      </div>

      <slot name="footer" :accommodations="accommodations"></slot>

      <!-- Branding Footer - only shows for Free plan users -->
      <BrandingFooter :userSettings="userSettings" />
    </div>

    <!-- Terms and Conditions Modal -->
    <TermsModal
      v-if="showTerms"
      :termsContent="userSettings.booking_terms_and_conditions"
      @close="showTerms = false"
      @accept="acceptTerms"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, defineComponent } from 'vue';

// Import components
import LoadingState from './LoadingState.vue';
import ErrorState from './ErrorState.vue';
import AccommodationList from './AccommodationList.vue';
import AccommodationDetail from './AccommodationDetail.vue';
import AvailabilityChecker from './AvailabilityChecker.vue';
import GroupAvailabilitySearch from './GroupAvailabilitySearch.vue';
import TermsModal from './TermsModal.vue';
import BrandingFooter from './BrandingFooter.vue';

// Define component name for better DevTools experience
const componentName = 'AvailabilitySearch';

// This helps with DevTools identification
defineComponent({
  name: componentName
});

// Get base URL from environment variable or use default
const API_BASE_URL = process.env.VITE_API_BASE_URL || 'https://laravel-app.lndo.site';

// Define props
const props = defineProps({
  siteId: {
    type: String,
    required: true
  },
  groupId: {
    type: String,
    required: false,
    default: null
  }
});

// Define emits
const emit = defineEmits(['loaded', 'error', 'retry', 'update', 'accommodation-selected', 'availability-checked', 'booking-created', 'search-completed']);

// Reactive state
const loading = ref(true);
const error = ref(null);
const accommodations = ref([]);
const selectedAccommodation = ref(null);
const componentVersion = __VERSION__;
const instance = getCurrentInstance();
const authToken = ref(null);
const authInProgress = ref(false);
const userSettings = ref({
  booking_terms_and_conditions: '',
  subscription_plan: 'Free' // Default to Free plan until we get the actual value from the API
});
const showTerms = ref(false);
const useGroupSearch = ref(true); // Use group search when groupId is provided

// Methods
const emitEvent = (type, detail = {}) => {
  // Emit Vue event
  emit(type, detail);

  // Also dispatch DOM custom event for non-Vue environments
  const event = new CustomEvent(`bookingbear:${type}`, {
    bubbles: true,
    composed: true, // Allows event to cross shadow DOM boundary
    detail: { ...detail, source: 'booking-bear-component', version: componentVersion }
  });

  if (instance?.proxy?.$el) {
    instance.proxy.$el.dispatchEvent(event);
  }
};

const getCachedData = () => {
  try {
    const cacheKey = `bookingbear-cache:${API_BASE_URL}/api/accommodations`;
    const cachedItem = localStorage.getItem(cacheKey);

    if (cachedItem) {
      const { data, timestamp } = JSON.parse(cachedItem);
      const cacheAge = Date.now() - timestamp;

      // Use cache if less than 5 minutes old
      if (cacheAge < 5 * 60 * 1000) {
        return data;
      }
    }
  } catch (err) {
    console.warn('Cache retrieval failed:', err);
  }

  return null;
};

const setCachedData = (newData) => {
  try {
    const cacheKey = `bookingbear-cache:${API_BASE_URL}/api/accommodations`;
    const cacheItem = JSON.stringify({
      data: newData,
      timestamp: Date.now()
    });

    localStorage.setItem(cacheKey, cacheItem);
  } catch (err) {
    console.warn('Cache storage failed:', err);
  }
};

const fetchData = async (silent = false) => {
  if (!silent) {
    loading.value = true;
  }

  // Clear any previous errors
  error.value = null;

  try {
    // Ensure we have a valid auth token
    if (!authToken.value) {
      const authenticated = await authenticate();
      if (!authenticated) {
        // The authenticate method already sets the error message
        return;
      }
    }

    // Build URL with query parameters
    let url = `${API_BASE_URL}/api/accommodations?published=true`;
    if (props.groupId) {
      url += `&group_id=${encodeURIComponent(props.groupId)}`;
    }
    
    const response = await fetch(url, {
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      // If unauthorized, try to re-authenticate once
      if (response.status === 401) {
        const authenticated = await authenticate();
        if (authenticated) {
          // Retry the request with the new token
          const retryResponse = await fetch(url, {
            headers: getAuthHeaders()
          });

          if (!retryResponse.ok) {
            // Create a user-friendly error message based on the status code
            let errorMessage;

            switch (retryResponse.status) {
              case 403:
                errorMessage = "You don't have permission to view these accommodations.";
                break;
              case 404:
                errorMessage = "The accommodations could not be found.";
                break;
              case 500:
                errorMessage = "The booking service is currently experiencing technical difficulties.";
                break;
              default:
                errorMessage = `Unable to load accommodations (Error ${retryResponse.status}).`;
            }

            throw new Error(errorMessage);
          }

          const newData = await retryResponse.json();
          processAccommodationsData(newData);
          return;
        } else {
          // The authenticate method already sets the error message
          return;
        }
      } else {
        // Create a user-friendly error message based on the status code
        let errorMessage;

        switch (response.status) {
          case 403:
            errorMessage = "You don't have permission to view these accommodations.";
            break;
          case 404:
            errorMessage = "The accommodations could not be found.";
            break;
          case 500:
            errorMessage = "The booking service is currently experiencing technical difficulties.";
            break;
          default:
            errorMessage = `Unable to load accommodations (Error ${response.status}).`;
        }

        throw new Error(errorMessage);
      }
    }

    const newData = await response.json();
    processAccommodationsData(newData);
  } catch (err) {
    // Use the error message from the Error object, or a generic message if none exists
    error.value = err.message || "Failed to load accommodations. Please try again later.";
    console.error('API fetch error:', err);
    emitEvent('error', { error: error.value, originalError: err });
  } finally {
    loading.value = false;
  }
};

// Process accommodations data from API response
const processAccommodationsData = (newData) => {
  // Only trigger UI updates if data actually changed
  if (JSON.stringify(accommodations.value) !== JSON.stringify(newData.data)) {
    // Process accommodations to set location from city and province
    accommodations.value = newData.data.map(accommodation => {
      // Create location string from city and province
      if (accommodation.city && accommodation.province) {
        accommodation.location = `${accommodation.city}, ${accommodation.province}`;
      } else if (accommodation.city) {
        accommodation.location = accommodation.city;
      } else if (accommodation.province) {
        accommodation.location = accommodation.province;
      }

      // Set base price from default_price
      if (accommodation.default_price) {
        accommodation.base_price = `R${accommodation.default_price}`;
      }

      return accommodation;
    });

    emitEvent('update', { accommodations: accommodations.value });
  }

  error.value = null;
  setCachedData(newData);
  emitEvent('loaded', { accommodations: accommodations.value });
};

const progressiveLoad = () => {
  // Start with placeholder or cached data if available
  const cachedData = getCachedData();
  if (cachedData) {
    accommodations.value = cachedData.data;
    loading.value = false;
    emitEvent('cache-loaded', { source: 'cache' });
  }

  // Then fetch fresh data
  fetchData();
};

const selectAccommodation = (accommodation) => {
  selectedAccommodation.value = accommodation;
  emitEvent('accommodation-selected', { accommodation });
};

// Authentication method
const authenticate = async () => {
  if (authInProgress.value) return;

  authInProgress.value = true;

  try {
    // Get the current domain
    const domain = window.location.origin;

    // Call the widget-auth endpoint
    const response = await fetch(`${API_BASE_URL}/api/widget-auth`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-Widget-Version': componentVersion
      },
      body: JSON.stringify({
        site_id: props.siteId,
        domain: domain
      })
    });

    // Handle different error scenarios with user-friendly messages
    if (!response.ok) {
      let errorMessage;

      switch (response.status) {
        case 401:
          errorMessage = "This website is not authorized to display accommodations. Please contact the accommodation provider.";
          break;
        case 404:
          errorMessage = "The booking service could not be found. Please check your site ID or contact the accommodation provider.";
          break;
        case 422:
          errorMessage = "Invalid site configuration. Please contact the accommodation provider.";
          break;
        case 500:
          errorMessage = "The booking service is currently experiencing technical difficulties. Please try again later.";
          break;
        default:
          errorMessage = `Connection error (${response.status}). Please try again later or contact the accommodation provider.`;
      }

      throw new Error(errorMessage);
    }

    const result = await response.json();

    if (!result.token) {
      throw new Error('Unable to connect to the booking service. Please try again later.');
    }

    // Store the token
    authToken.value = result.token;

    return true;
  } catch (err) {
    console.error('Authentication error:', err);
    // Set a user-friendly error message
    error.value = err.message || "Unable to connect to the booking service. Please try again later.";
    emitEvent('error', { error: error.value, originalError: err });
    return false;
  } finally {
    authInProgress.value = false;
    // Ensure loading state is reset if authentication fails
    if (error.value) {
      loading.value = false;
    }
  }
};

// Get authorization headers for API requests
const getAuthHeaders = () => {
  const headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'X-Widget-Version': componentVersion
  };

  if (authToken.value) {
    headers['Authorization'] = `Bearer ${authToken.value}`;
  }

  return headers;
};

// Fetch user settings
const fetchUserSettings = async () => {
  try {
    // Ensure we have a valid auth token
    if (!authToken.value) {
      const authenticated = await authenticate();
      if (!authenticated) {
        throw new Error('Authentication failed');
      }
    }

    const response = await fetch(`${API_BASE_URL}/api/user/settings`, {
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      // If unauthorized, try to re-authenticate once
      if (response.status === 401) {
        const authenticated = await authenticate();
        if (authenticated) {
          // Retry the request with the new token
          const retryResponse = await fetch(`${API_BASE_URL}/api/user/settings`, {
            headers: getAuthHeaders()
          });

          if (!retryResponse.ok) {
            throw new Error(`API error after re-authentication: ${retryResponse.status}`);
          }

          const settingsData = await retryResponse.json();
          userSettings.value = settingsData;
          return;
        } else {
          throw new Error('Re-authentication failed');
        }
      } else {
        throw new Error(`API error: ${response.status}`);
      }
    }

    const settingsData = await response.json();
    userSettings.value = settingsData;
  } catch (err) {
    console.error('Failed to fetch user settings:', err.message);
    // Don't set error.value here as it would disrupt the main UI
    // Just log the error and continue with default values
  }
};

// Event handlers
const handleAvailabilityResult = (data) => {
  emitEvent('availability-checked', data);
};

const handleBookingCreated = (data) => {
  emitEvent('booking-created', data);
};

const handleError = (data) => {
  error.value = data.error;
  emitEvent('error', data);
};

const handleSearchCompleted = (data) => {
  emitEvent('search-completed', data);
};

const resetBooking = () => {
  // Reset to accommodation selection view
  selectedAccommodation.value = null;
};

// Handle terms and conditions
const acceptTerms = () => {
  showTerms.value = false;
};

// Lifecycle hooks
onMounted(() => {
  emitEvent('mounted', { version: componentVersion });
  authenticate().then(success => {
    if (success) {
      // Only load accommodations if not using group search mode
      if (!props.groupId || !useGroupSearch.value) {
        progressiveLoad();
      } else {
        // For group search mode, set loading to false since we don't need to load accommodations
        loading.value = false;
      }
      fetchUserSettings(); // Fetch user settings after authentication
    }
  });
});

// Expose methods to template
defineExpose({
  fetchData,
  selectAccommodation,
  resetBooking,
  fetchUserSettings
});
</script>

<style>
/* No styles here as they are imported in the main component */
</style>
