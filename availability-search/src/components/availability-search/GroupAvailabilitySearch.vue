<template>
  <div class="group-availability-search">
    <div class="search-form">
      <h3>Search Available Accommodations</h3>
      
      <DateRangePicker 
        @date-range-selected="setDateRange" 
        :calendar-data="calendarData" 
      />
      
      <OccupancySelector 
        v-model="numberOfPersons" 
        :minOccupancy="1"
        :maxOccupancy="20" 
        :showLimits="false" 
      />
      
      <button 
        class="search-availability-button" 
        @click="searchAvailability"
        :disabled="!dateRange.start || !dateRange.end || isSearching"
      >
        {{ isSearching ? 'Searching...' : 'Search Availability' }}
      </button>
    </div>

    <!-- Search Results -->
    <div v-if="searchResults" class="search-results">
      <div v-if="searchResults.has_availability" class="results-summary">
        <h4>Available Accommodations</h4>
        <p>{{ searchResults.available_accommodations }} of {{ searchResults.total_accommodations }} accommodations available for your dates</p>

        <AvailableAccommodationsList
          :accommodations="availableAccommodations"
          :allAccommodations="searchResults.accommodations"
          :dateRange="dateRange"
          :numberOfPersons="numberOfPersons"
          :apiBaseUrl="apiBaseUrl"
          @book-now="handleBookNow"
        />
      </div>

      <div v-else class="no-availability">
        <div class="availability-icon">✗</div>
        <div class="availability-message">
          <h4>No Accommodations Available</h4>
          <p>Sorry, no accommodations are available for the selected dates.</p>
          <p>Please try different dates or adjust your search criteria.</p>

          <!-- Show specific reasons if available -->
          <div v-if="searchResults.accommodations && searchResults.accommodations.length > 0" class="unavailability-reasons">
            <h5>Reasons:</h5>
            <ul>
              <li v-for="reason in uniqueUnavailabilityReasons" :key="reason">
                {{ getReasonDescription(reason) }}
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- Error State -->
    <div v-if="searchError" class="search-error">
      <div class="error-icon">⚠</div>
      <div class="error-message">
        <h4>Search Error</h4>
        <p>{{ searchError }}</p>
        <button @click="retrySearch" class="retry-button">Try Again</button>
      </div>
    </div>

    <!-- Booking Form Modal/Section -->
    <div v-if="selectedAccommodation" class="booking-section">
      <div class="booking-header">
        <button class="back-button" @click="selectedAccommodation = null">
          ← Back to Results
        </button>
        <h4>Book {{ selectedAccommodation.name }}</h4>
      </div>
      
      <div class="booking-content">
        <!-- Left column: Booking form -->
        <BookingForm 
          :accommodationId="selectedAccommodation.id" 
          :isSubmitting="bookingInProgress"
          @submit="submitBooking" 
          @show-terms="$emit('show-terms')" 
        />
        
        <!-- Right column: Pricing breakdown -->
        <PricingBreakdown 
          :availabilityResult="selectedAccommodationDetails" 
          :dateRange="dateRange"
          :numberOfPersons="numberOfPersons" 
        />
      </div>
    </div>

    <!-- Booking Success View -->
    <BookingSuccess 
      v-if="bookingSuccess" 
      :accommodation="selectedAccommodation" 
      :dateRange="dateRange"
      :numberOfPersons="numberOfPersons" 
      :totalPrice="selectedAccommodationDetails?.total_price"
      :bookingReference="bookingReference" 
      :notes="bookingForm.notes" 
      @reset="resetBooking" 
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import DateRangePicker from './DateRangePicker.vue';
import OccupancySelector from './OccupancySelector.vue';
import AvailableAccommodationsList from './AvailableAccommodationsList.vue';
import BookingForm from './BookingForm.vue';
import PricingBreakdown from './PricingBreakdown.vue';
import BookingSuccess from './BookingSuccess.vue';

const props = defineProps({
  groupId: {
    type: [String, Number],
    required: true
  },
  apiBaseUrl: {
    type: String,
    required: true
  },
  authToken: {
    type: String,
    required: true
  },
  componentVersion: {
    type: String,
    required: true
  }
});

const emit = defineEmits([
  'search-completed',
  'booking-created',
  'error',
  'show-terms'
]);

// State
const dateRange = ref({ start: null, end: null });
const numberOfPersons = ref(2);
const searchResults = ref(null);
const isSearching = ref(false);
const searchError = ref(null);
const selectedAccommodation = ref(null);
const selectedAccommodationDetails = ref(null);
const bookingInProgress = ref(false);
const bookingSuccess = ref(false);
const bookingReference = ref('');
const bookingForm = ref({});
const calendarData = ref({});

// Computed
const availableAccommodations = computed(() => {
  if (!searchResults.value?.accommodations) return [];
  return searchResults.value.accommodations.filter(acc => acc.available);
});

const uniqueUnavailabilityReasons = computed(() => {
  if (!searchResults.value?.accommodations) return [];
  const reasons = searchResults.value.accommodations
    .filter(acc => !acc.available)
    .map(acc => acc.reason)
    .filter(reason => reason);
  return [...new Set(reasons)];
});

// Methods
const setDateRange = (range) => {
  dateRange.value = range;
  // Clear previous search results and errors when dates change
  if (searchResults.value) {
    searchResults.value = null;
    selectedAccommodation.value = null;
  }
  if (searchError.value) {
    searchError.value = null;
  }
};

const getAuthHeaders = () => {
  return {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'X-Widget-Version': props.componentVersion,
    'Authorization': `Bearer ${props.authToken}`
  };
};

const normalizeToLocalMidnight = (date) => {
  const localDate = new Date(date);
  localDate.setMinutes(localDate.getMinutes() - localDate.getTimezoneOffset());
  return localDate.toISOString().split('T')[0];
};

const searchAvailability = async () => {
  if (!dateRange.value.start || !dateRange.value.end) {
    return;
  }

  isSearching.value = true;
  searchResults.value = null;
  searchError.value = null;
  selectedAccommodation.value = null;

  try {
    const url = `${props.apiBaseUrl}/api/accommodation-groups/${props.groupId}/check-availability`;
    const requestData = {
      start_date: normalizeToLocalMidnight(dateRange.value.start),
      end_date: normalizeToLocalMidnight(dateRange.value.end),
      number_of_persons: numberOfPersons.value
    };

    const response = await fetch(url, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(requestData)
    });

    if (!response.ok) {
      let errorMessage;
      switch (response.status) {
        case 404:
          errorMessage = 'Accommodation group not found or you do not have permission to access it.';
          break;
        case 422:
          const validationError = await response.json();
          errorMessage = validationError.message || 'Invalid search parameters.';
          break;
        case 401:
          errorMessage = 'Authentication failed. Please refresh the page and try again.';
          break;
        default:
          errorMessage = `Search failed (Error ${response.status}). Please try again.`;
      }
      throw new Error(errorMessage);
    }

    const result = await response.json();
    searchResults.value = result;
    searchError.value = null; // Clear any previous errors
    emit('search-completed', { result });
  } catch (err) {
    searchError.value = err.message;
    emit('error', { error: err.message, originalError: err });
  } finally {
    isSearching.value = false;
  }
};

const retrySearch = () => {
  searchError.value = null;
  searchAvailability();
};

const getReasonDescription = (reason) => {
  const descriptions = {
    'minimum_notice': 'Some accommodations require more advance booking notice',
    'minimum_stay': 'Some accommodations have minimum stay requirements',
    'existing_booking': 'Some accommodations are already booked',
    'blocked_period': 'Some accommodations have blocked periods',
    'occupancy_limit': 'Some accommodations cannot accommodate the requested number of guests'
  };
  return descriptions[reason] || 'Some accommodations are not available for other reasons';
};

const handleBookNow = (accommodation) => {
  selectedAccommodation.value = accommodation;
  selectedAccommodationDetails.value = {
    available: true,
    total_price: accommodation.total_price,
    details: accommodation.details
  };
};

const submitBooking = async (formData) => {
  bookingInProgress.value = true;
  bookingForm.value = formData;

  try {
    const bookingData = {
      accommodation_id: selectedAccommodation.value.id,
      first_name: formData.first_name,
      last_name: formData.last_name,
      email: formData.email,
      contact_number: formData.contact_number,
      start_date: normalizeToLocalMidnight(dateRange.value.start),
      end_date: normalizeToLocalMidnight(dateRange.value.end),
      occupancy: numberOfPersons.value,
      booking_status_id: 2, // Pending status
      notes: formData.notes
    };

    const response = await fetch(`${props.apiBaseUrl}/api/bookings`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(bookingData)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to create booking');
    }

    const result = await response.json();
    bookingReference.value = `#${result.booking.id}`;
    bookingSuccess.value = true;
    emit('booking-created', { booking: result.booking });
  } catch (err) {
    emit('error', { error: `Failed to create booking: ${err.message}`, originalError: err });
  } finally {
    bookingInProgress.value = false;
  }
};

const resetBooking = () => {
  selectedAccommodation.value = null;
  selectedAccommodationDetails.value = null;
  bookingSuccess.value = false;
  bookingReference.value = '';
  bookingForm.value = {};
};

// Fetch calendar data for date picker restrictions
const fetchCalendarData = async () => {
  try {
    // For group availability, we might want to fetch group-level calendar data
    // For now, we'll use a basic approach or extend the API later
    const response = await fetch(`${props.apiBaseUrl}/api/accommodation-groups/${props.groupId}/calendar-data`, {
      headers: getAuthHeaders()
    });
    
    if (response.ok) {
      calendarData.value = await response.json();
    }
  } catch (err) {
    console.warn('Failed to fetch calendar data:', err.message);
    // Continue without calendar restrictions
  }
};

onMounted(() => {
  fetchCalendarData();
});
</script>

<style>
/* Styles will be imported from separate CSS files */
</style>
