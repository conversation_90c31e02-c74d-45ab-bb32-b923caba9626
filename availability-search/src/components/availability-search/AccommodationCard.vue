<template>
  <div class="accommodation-card">
    <div class="accommodation-image">
      <div class="accommodation-image-overlay"></div>
      <img
        :src="getImageUrl(accommodation)"
        :alt="accommodation.name"
        @error="handleImageError"
      >
      <div v-if="accommodation.rating" class="accommodation-rating">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="currentColor" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="rating-star">
          <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
        </svg>
        <span>{{ accommodation.rating }}</span>
      </div>
    </div>
    <div class="accommodation-details">
      <div class="accommodation-info">
        <h3 class="accommodation-name">{{ accommodation.name }}</h3>
        <p class="accommodation-location" v-if="accommodation.location">
          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="location-icon">
            <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
            <circle cx="12" cy="10" r="3"></circle>
          </svg>
          {{ accommodation.location }}
        </p>
        <div class="accommodation-description" v-if="accommodation.description">
          {{ truncatedDescription }}
        </div>
      </div>
      <div class="accommodation-footer">
        <p class="accommodation-price" v-if="displayPrice">{{ priceText }}</p>
        <button @click.prevent="handleButtonClick">{{ buttonText }}</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  accommodation: {
    type: Object,
    required: true
  },
  apiBaseUrl: {
    type: String,
    required: true
  },
  buttonMode: {
    type: String,
    default: 'check-availability', // 'check-availability' or 'book-now'
    validator: (value) => ['check-availability', 'book-now'].includes(value)
  },
  totalPrice: {
    type: [String, Number],
    default: null
  },
  currency: {
    type: String,
    default: 'R'
  }
});

// Note: The accommodation object may include these properties:
// - name: string (required)
// - location: string (optional)
// - base_price: string (optional)
// - description: string (optional)
// - gallery_images: array (optional)
// - rating: number (optional) - e.g. 4.7

const emit = defineEmits(['select', 'book-now']);

const truncatedDescription = computed(() => {
  if (!props.accommodation.description) return '';
  const maxLength = 100;
  const desc = props.accommodation.description;
  return desc.length > maxLength ? `${desc.substring(0, maxLength)}...` : desc;
});

// Computed properties for button and price display
const buttonText = computed(() => {
  return props.buttonMode === 'book-now' ? 'Book Now' : 'Check availability';
});

const displayPrice = computed(() => {
  return props.buttonMode === 'book-now' ? props.totalPrice : props.accommodation.base_price;
});

const priceText = computed(() => {
  if (props.buttonMode === 'book-now' && props.totalPrice) {
    return `Total: <span>${props.currency}${props.totalPrice}</span>`;
  } else if (props.accommodation.base_price) {
    return `From <span>${props.accommodation.base_price}</span> / pn`;
  }
  return '';
});

// Methods
const handleButtonClick = () => {
  if (props.buttonMode === 'book-now') {
    emit('book-now', props.accommodation);
  } else {
    emit('select', props.accommodation);
  }
};

// Image handling methods
const getImageUrl = (accommodation, size = 'medium') => {
  // Default fallback image with full URL
  const fallbackImage = `${props.apiBaseUrl}/img/placeholder.jpg`;

  if (!accommodation || !accommodation.gallery_images || accommodation.gallery_images.length === 0) {
    return fallbackImage;
  }

  const image = accommodation.gallery_images[0];
  let imageUrl;

  // Try to get the requested size, fall back to original_url or full_url
  if (size === 'large' && image.large) {
    imageUrl = image.large;
  } else if (size === 'medium' && image.medium) {
    imageUrl = image.medium;
  } else if (size === 'thumbnail' && image.thumbnail) {
    imageUrl = image.thumbnail;
  } else {
    // Fall back to original_url or full_url
    imageUrl = image.original_url || image.full_url || fallbackImage;
  }

  // Ensure the URL is absolute
  if (imageUrl && !imageUrl.startsWith('http')) {
    // If it's a relative URL, make it absolute
    if (imageUrl.startsWith('/')) {
      return props.apiBaseUrl + imageUrl;
    } else {
      return `${props.apiBaseUrl}/${imageUrl}`;
    }
  }

  return imageUrl;
};

const handleImageError = (event) => {
  console.warn('Image failed to load:', event.target.src);

  // If the image fails to load, try a different URL format or fall back to placeholder
  const img = event.target;
  const originalSrc = img.src;

  try {
    // Extract domain from API_BASE_URL
    const baseUrlDomain = new URL(props.apiBaseUrl).hostname;

    // Try different approaches to fix the URL
    if (originalSrc.includes(baseUrlDomain)) {
      // If the URL is from our API domain, try removing the domain
      const relativeSrc = originalSrc.replace(props.apiBaseUrl, '');
      img.src = relativeSrc;
    } else if (originalSrc.startsWith('/')) {
      // If it's a relative URL, try adding the domain
      const absoluteSrc = props.apiBaseUrl + originalSrc;
      img.src = absoluteSrc;
    } else if (!originalSrc.startsWith('http')) {
      // If it's not an absolute URL, try adding the domain with a slash
      const absoluteSrc = `${props.apiBaseUrl}/${originalSrc}`;
      img.src = absoluteSrc;
    } else {
      // If all else fails, use a placeholder
      img.src = `${props.apiBaseUrl}/img/placeholder.jpg`;
    }
  } catch (error) {
    console.error('Error handling image:', error);
    img.src = `${props.apiBaseUrl}/img/placeholder.jpg`;
  }
};
</script>

<style>
/* Styles moved to separate CSS file */
</style>
