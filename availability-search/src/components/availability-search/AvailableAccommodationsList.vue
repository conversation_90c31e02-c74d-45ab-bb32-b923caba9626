<template>
  <div class="available-accommodations-list">
    <div class="accommodations-grid">
      <AccommodationCard
        v-for="accommodation in accommodations"
        :key="accommodation.id"
        :accommodation="transformAccommodation(accommodation)"
        :apiBaseUrl="apiBaseUrl"
        :buttonMode="'book-now'"
        :totalPrice="accommodation.total_price"
        :currency="accommodation.details?.currency || 'R'"
        @book-now="$emit('book-now', accommodation)"
      />
    </div>
    
    <!-- Show unavailable accommodations if any -->
    <div v-if="unavailableAccommodations.length > 0" class="unavailable-section">
      <h4>Currently Unavailable</h4>
      <div class="unavailable-accommodations">
        <div 
          v-for="accommodation in unavailableAccommodations" 
          :key="accommodation.id"
          class="unavailable-accommodation"
        >
          <div class="accommodation-info">
            <h5>{{ accommodation.name }}</h5>
            <p class="unavailable-reason">{{ getUnavailableMessage(accommodation) }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import AccommodationCard from './AccommodationCard.vue';

const props = defineProps({
  accommodations: {
    type: Array,
    required: true
  },
  dateRange: {
    type: Object,
    required: true
  },
  numberOfPersons: {
    type: Number,
    required: true
  },
  apiBaseUrl: {
    type: String,
    required: true
  },
  // All accommodations from the API response (including unavailable ones)
  allAccommodations: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['book-now']);

// Computed properties
const unavailableAccommodations = computed(() => {
  if (!props.allAccommodations.length) return [];
  return props.allAccommodations.filter(acc => !acc.available);
});

// Methods
const transformAccommodation = (accommodation) => {
  // Transform the accommodation object from the group availability API
  // to match the format expected by AccommodationCard
  return {
    id: accommodation.id,
    name: accommodation.name,
    // We don't have location, description, rating, or gallery_images from the group API
    // These would need to be fetched separately or included in the API response
    location: null,
    description: `Available for ${props.numberOfPersons} guest${props.numberOfPersons > 1 ? 's' : ''}`,
    rating: null,
    gallery_images: [],
    // Set the base price from the total price for display
    base_price: accommodation.total_price ? `${accommodation.details?.currency || 'R'}${accommodation.total_price}` : null,
    // Add the availability details
    availability_details: accommodation.details,
    total_price: accommodation.total_price,
    currency: accommodation.details?.currency || 'R',
    // Add occupancy info for display
    min_occupancy: accommodation.details?.min_occupancy || 1,
    max_occupancy: accommodation.details?.max_occupancy || 10
  };
};

const getUnavailableMessage = (accommodation) => {
  const reasonMessages = {
    'minimum_notice': 'Booking notice period not met',
    'minimum_stay': 'Stay duration below minimum requirement',
    'existing_booking': 'Already booked for these dates',
    'blocked_period': 'Unavailable period',
    'occupancy_limit': 'Occupancy limit exceeded'
  };
  
  return accommodation.message || reasonMessages[accommodation.reason] || 'Not available for selected dates';
};
</script>

<style>
.available-accommodations-list {
  margin-top: var(--spacing-lg);
}

.accommodations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.unavailable-section {
  margin-top: var(--spacing-lg);
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--color-border);
}

.unavailable-section h4 {
  color: var(--color-text-light);
  margin-bottom: var(--spacing-md);
  font-size: 1.1em;
}

.unavailable-accommodations {
  display: grid;
  gap: var(--spacing-sm);
}

.unavailable-accommodation {
  padding: var(--spacing-md);
  background: #f9f9f9;
  border-radius: var(--border-radius);
  border-left: 4px solid #ccc;
}

.unavailable-accommodation .accommodation-info h5 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--color-text);
  font-size: 1em;
}

.unavailable-accommodation .unavailable-reason {
  margin: 0;
  color: var(--color-text-light);
  font-size: 0.9em;
}

@media (max-width: 768px) {
  .accommodations-grid {
    grid-template-columns: 1fr;
  }
}
</style>
