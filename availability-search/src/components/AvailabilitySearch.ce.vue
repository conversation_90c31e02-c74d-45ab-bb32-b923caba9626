<template>
  <AvailabilitySearchMain :siteId="siteId" :groupId="groupId" />
</template>

<script setup>
import { defineComponent } from 'vue';
import AvailabilitySearchMain from './availability-search/AvailabilitySearchMain.vue';
import '../styles/variables.css';
import '@sebarkar/vue3-hotel-datepicker/dist/style.css';

// Define component name for better DevTools experience
defineComponent({
  name: 'AvailabilitySearch'
});

// Define props
const props = defineProps({
  siteId: {
    type: String,
    required: true
  },
  groupId: {
    type: String,
    required: false,
    default: null
  }
});
</script>

<style>
/* Import all component styles here to ensure they're included in the shadow DOM */
@import '../styles/variables.css';
@import '../../node_modules/@sebarkar/vue3-hotel-datepicker/dist/style.css';
@import './availability-search/styles/loading-state.css';
@import './availability-search/styles/error-state.css';
@import './availability-search/styles/accommodation-list.css';
@import './availability-search/styles/accommodation-card.css';
@import './availability-search/styles/accommodation-detail.css';
@import './availability-search/styles/date-range-picker.css';
@import './availability-search/styles/occupancy-selector.css';
@import './availability-search/styles/availability-checker.css';
@import './availability-search/styles/booking-form.css';
@import './availability-search/styles/pricing-breakdown.css';
@import './availability-search/styles/booking-success.css';
@import './availability-search/styles/terms-modal.css';
@import './availability-search/styles/not-available-message.css';
@import './availability-search/styles/branding-footer.css';
@import './availability-search/styles/group-availability-search.css';
@import './availability-search/styles/main.css';

:host {
  /* Map external CSS variables to internal ones */
  --color-background: var(--bb-color-background, #E5F3FF);
  --color-primary: var(--bb-color-primary, #003B5C);
  --color-accent-primary: var(--bb-color-accent-primary, #009B77);
  --color-accent-secondary: var(--bb-color-accent-secondary, #FFBF00);
  --color-accent-cta: var(--bb-color-accent-cta, #F67891);

  /* Text colors */
  --color-text: #333;
  --color-text-light: #666;

  /* Border and spacing */
  --color-border: #ddd;
  --border-radius: 8px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;

  /* Effects */
  --shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  /* Allow component to inherit custom properties from parent */
  color: var(--color-text);
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  display: block;
  width: 100%;
}
</style>
