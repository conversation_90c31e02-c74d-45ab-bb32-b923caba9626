<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Group Availability Search Test</title>
    <style>
        body {
            font-family: system-ui, -apple-system, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        h1 {
            color: #003B5C;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-info {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-info h2 {
            margin-top: 0;
            color: #003B5C;
        }
        .test-info p {
            margin: 10px 0;
            color: #666;
        }
        .test-info code {
            background: #f0f0f0;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: monospace;
        }
        .widget-container {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Group Availability Search Test</h1>
        
        <div class="test-info">
            <h2>Test Instructions</h2>
            <p>This page tests the new group availability search functionality. The widget should:</p>
            <ul>
                <li>Show a date picker and occupancy selector</li>
                <li>Allow searching for available accommodations in a group</li>
                <li>Display results with "Book Now" buttons instead of "Check Availability"</li>
                <li>Open booking form when "Book Now" is clicked</li>
                <li>Handle errors gracefully</li>
            </ul>
            <p><strong>Required:</strong> Set <code>groupId</code> prop to test group search mode</p>
            <p><strong>API Base URL:</strong> <code>https://laravel-app.lndo.site</code> (or your local Laravel backend)</p>
        </div>

        <div class="widget-container">
            <!-- Widget will be embedded here -->
            <availability-search 
                site-id="test-site-123" 
                group-id="1"
                style="
                    --bb-color-background: #E5F3FF;
                    --bb-color-primary: #003B5C;
                    --bb-color-accent-primary: #009B77;
                    --bb-color-accent-secondary: #FFBF00;
                    --bb-color-accent-cta: #F67891;
                "
            ></availability-search>
        </div>
    </div>

    <!-- Load the widget script -->
    <script type="module">
        // This would normally load the built widget
        // For testing, you'd need to build the component first
        console.log('Widget container ready');
        
        // Listen for widget events
        document.addEventListener('bookingbear:search-completed', (event) => {
            console.log('Search completed:', event.detail);
        });
        
        document.addEventListener('bookingbear:booking-created', (event) => {
            console.log('Booking created:', event.detail);
        });
        
        document.addEventListener('bookingbear:error', (event) => {
            console.log('Widget error:', event.detail);
        });
    </script>
</body>
</html>
